# 账号被踢下线问题修复

## 问题描述
之前A在登录时，App在前台，这时B再登录同一个账号，这时A的账号会被顶掉，然后回到登录页面。现在修改过代码后，在B登录后A不会自动回到登录页面。

## 问题根因
1. **onKickedOffline回调使用错误的logout方式**：
   - 原代码：`LoginUtils.logOut()` (无force参数)
   - 问题：会调用`ApiService().logout()`，如果token已过期，API调用失败，不会执行页面跳转

2. **防重复机制可能阻止踢下线处理**：
   - LoginUtils的`_isLoggingOut`标志可能阻止踢下线场景的logout执行

## 修复方案

### 1. 修改onKickedOffline回调 (chat_im_manager.dart)
```dart
onKickedOffline: () {
  // The current user has been kicked off, by other devices
  debugPrint("账号被踢下线，强制退出登录");
  LoginUtils.logOut(force: true, fromKickedOffline: true);
},
```

### 2. 优化LoginUtils.logOut方法 (login_utils.dart)
- 添加`fromKickedOffline`参数
- 踢下线场景可以绕过`_isLoggingOut`检查
- 确保踢下线时强制执行logout流程

```dart
static logOut({bool? force, bool? fromKickedOffline}) async {
  // 防止重复调用logout，但踢下线场景需要强制执行
  if (_isLoggingOut && fromKickedOffline != true) {
    print('------------------- logout already in progress, skipping');
    return;
  }
  // ... 后续逻辑
}
```

## 修复效果
- ✅ **踢下线强制logout**：使用`force: true`避免API调用失败
- ✅ **绕过防重复机制**：踢下线场景可以强制执行logout
- ✅ **确保页面跳转**：强制logout会直接跳转到登录页面
- ✅ **保持原有防护**：其他场景仍然有防重复保护

## 测试建议
1. A设备登录账号，保持App在前台
2. B设备登录同一账号
3. 验证A设备是否立即跳转到登录页面
4. 验证控制台日志显示"账号被踢下线，强制退出登录"
