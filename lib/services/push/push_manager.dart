import 'dart:convert';

import 'package:dada/configs/app_config.dart';
import 'package:dada/model/global_notice_entity.dart';
import 'package:dada/pages/global_speaker/global_screen_overlay_controller.dart';
import 'package:dada/services/network/api.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_push/common/common_defines.dart';
import 'package:tencent_cloud_chat_push/common/tim_push_listener.dart';
import 'package:tencent_cloud_chat_push/tencent_cloud_chat_push.dart';

class PushManager extends GetxService {
  static PushManager? _sharedInstance;

  static PushManager get sharedInstance =>
      _sharedInstance ??= PushManager._internal();

  late TIMPushListener _listener;

  PushManager._internal();

  Future<void> initPush() async {
    TencentCloudChatPush().disablePostNotificationInForeground(disable: true);
    TencentCloudChatPushResult result =
        await TencentCloudChatPush().registerPush(
      sdkAppId:
          ApiConfig.env == Env.dev ? AppConfig.imAppId_Dev : AppConfig.imAppId,
      appKey: AppConfig.pushAppKey,
      apnsCertificateID:
          kDebugMode ? AppConfig.pushCertID_Dev : AppConfig.pushCertID_Dis,
      onNotificationClicked: _onNotificationClicked,
    );
    if (result.code != 0) {
      debugPrint("PushManager registerPush failed!");
    } else {
      addListener();
    }
  }

  void addListener() {
    _listener = TIMPushListener(
      onRecvPushMessage: (message) {
        if (message.ext != null) {
          GlobalNoticeEntity globalNoticeEntity =
              GlobalNoticeEntity.fromJson(jsonDecode(message.ext!));
          if (globalNoticeEntity.type == 2) {
            if (Get.isRegistered<GlobalScreenOverlayController>()) {
              Get.find<GlobalScreenOverlayController>()
                  .addNotice(globalNoticeEntity);
            }
          } else if (globalNoticeEntity.type == 1) {}
        }
        debugPrint("PushManager onRecvPushMessage: $message");
      },
      onRevokePushMessage: (message) {
        debugPrint("PushManager onRevokePushMessage: $message");
      },
      onNotificationClicked: (message) {
        debugPrint("PushManager onNotificationClicked: $message");
      },
    );
    TencentCloudChatPush().addPushListener(listener: _listener);
  }

  void removeListener() {
    TencentCloudChatPush().removePushListener(listener: _listener);
  }

  void unRegisterPush() async {
    removeListener();
    TencentCloudChatPushResult result =
        await TencentCloudChatPush().unRegisterPush();
    if (result.code != 0) {
      debugPrint("PushManager unRegisterPush failed!");
    }
  }

  void _onNotificationClicked(
      {required String ext, String? userID, String? groupID}) {
    debugPrint(
        "PushManager _onNotificationClicked: \next:$ext, \nuserID:$userID, \ngroupID:$groupID");
  }
}
