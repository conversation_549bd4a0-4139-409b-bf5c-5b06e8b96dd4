import 'dart:convert';
import 'dart:ui';

import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/json/base/json_convert_content.dart';
import 'package:dada/model/chat_room_seat_info_entity.dart';
import 'package:dada/model/topic_item_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/chat/contacts/contacts_controller.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_controller.dart';
import 'package:dada/pages/chat/detail/chat_detail_controller.dart';
import 'package:dada/pages/chat/setting/private_chat_setting_controller.dart';
import 'package:dada/pages/main/main_controller.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_callback.dart';
import 'package:dada/services/im/mixins/chat_conversation_mixin.dart';
import 'package:dada/services/im/mixins/chat_group_mixin.dart';
import 'package:dada/services/im/mixins/chat_message_mixin.dart';
import 'package:dada/services/im/mixins/friend_ship_mixin.dart';
import 'package:dada/services/network/api.dart';
import 'package:dada/services/push/push_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/login_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_self_info_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/tim_uikit_config.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ChatIMManager extends GetxService
    with
        ChatConversationMixin,
        FriendShipMixin,
        ChatGroupMixin,
        ChatMessageMixin {
  static ChatIMManager? _sharedInstance;

  static ChatIMManager get sharedInstance {
    _sharedInstance ??= ChatIMManager._internal();
    Get.put<ChatIMManager>(_sharedInstance!, permanent: true);
    return _sharedInstance!;
  }

  ChatIMManager._internal() {
    _initTIMUIKit();
    _registerListenerCallback();
  }

  ///腾讯IM UIKit
  final CoreServicesImpl _coreInstance = TIMUIKitCore.getInstance();
  final V2TIMManager _sdkInstance = TIMUIKitCore.getSDKInstance();

  ///腾讯IM SDK 高级消息的事件监听器
  late V2TimAdvancedMsgListener _advancedMsgListener;

  ///群组监听器
  late V2TimGroupListener _groupListener;

  ///会话监听器
  late V2TimConversationListener _conversationListener;

  ///好友关系监听器
  late V2TimFriendshipListener _friendshipListener;

  bool imSDKInitSuccess = false;
  bool imLogined = false;
  bool isLogging = false;

  ///用户签名（服务端生成，调试测试可由GenerateUserSig.dart生成）
  String? _userSig;

  get userSig => _userSig;

  final List<ChatImListener> _listeners = [];

  DateTime? _lastUpdateContactsListDate;
  DateTime? _lastUpdateConversationListDate;

  ///初始化 TimUIKit
  void _initTIMUIKit() async {
    final initSuccess = await _coreInstance.init(
      sdkAppID:
          ApiConfig.env == Env.dev ? AppConfig.imAppId_Dev : AppConfig.imAppId,
      loglevel: kDebugMode
          ? LogLevelEnum.V2TIM_LOG_DEBUG
          : LogLevelEnum.V2TIM_LOG_NONE,
      config: const TIMUIKitConfig(
        isShowOnlineStatus: false,
        isCheckDiskStorageSpace: true,
      ),
      listener: V2TimSDKListener(
        onConnectFailed: (code, error) {
          debugPrint("即时通信服务连接失败");
        },
        onConnectSuccess: () {
          // Connected to Tencent IM server success
          debugPrint("即时通信服务连接成功");
        },
        onConnecting: () {
          debugPrint("即时通信服务连接中...");
        },
        onKickedOffline: () {
          // The current user has been kicked off, by other devices
          debugPrint("账号被踢下线，强制退出登录");
          LoginUtils.logOut(force: true, fromKickedOffline: true);
        },
        onSelfInfoUpdated: (info) {
          debugPrint("信息已变更");
        },
        onUserSigExpired: () {
          // The UserSig is expired
          for (var element in _listeners) {
            element.onUserSigExpired?.call();
          }
          LoginUtils.logOut(force: true);
        },
        onUserStatusChanged: (List<V2TimUserStatus> userStatusList) {
          // The onUserStatus Changed
        },
      ),
      onTUIKitCallbackListener: (TIMCallback callback) {
        switch (callback.type) {
          case TIMCallbackType.INFO:
            ToastUtils.showToast(callback.infoRecommendText!);
            break;
          default:
            if (callback.errorCode == 10059 ||
                callback.errorCode == 7014 ||
                callback.errorCode == 10010 ||
                callback.errorCode == -1) {
              debugPrint(callback.errorMsg ?? callback.errorCode.toString());
            } else if (callback.errorCode == 80004 ||
                callback.errorCode == 80001) {
              ToastUtils.showToast("发送内容违规！");
            } else {
              ToastUtils.showToast(
                  callback.errorMsg ?? callback.errorCode.toString());
            }
            break;
        }
      },
    );
    if (initSuccess == null || !initSuccess) {
      if (kDebugMode) {
        ToastUtils.showToast("即时通信IM SDK初始化失败!");
      }
    } else {
      imSDKInitSuccess = true;
      setTUITheme();
    }
  }

  void setTUITheme() {
    final themeJson = TUITheme.light.toJson();
    themeJson["chatMessageItemFromSelfBgColor"] = const Color(0xFFFFFFFF);
    themeJson["chatMessageItemFromOthersBgColor"] = const Color(0xFFFFFFFF);
    themeJson["chatBgColor"] = const Color(0xFFF5F5F5);
    final newTheme = TUITheme.fromJson(themeJson);
    _coreInstance.setTheme(theme: newTheme);
  }

  ///添加TIM消息高级监听器
  void _registerListenerCallback() {
    ///SDk Advance Msg Listener
    _advancedMsgListener = V2TimAdvancedMsgListener(
      onRecvNewMessage: (V2TimMessage msg) {
        debugPrint("ChatImManager _advancedMsgListener onRecvNewMessage!");
        for (var element in _listeners) {
          element.onReceiveNewMessage?.call(msg);
        }
        updateConversationList();
      },
    );
    _sdkInstance
        .getMessageManager()
        .addAdvancedMsgListener(listener: _advancedMsgListener);

    ///Group Listener
    _groupListener = V2TimGroupListener(onReceiveJoinApplication:
        (String groupID, V2TimGroupMemberInfo member, String opReason) {
      debugPrint("ChatIMManager onReceiveJoinApplication: $groupID, ");
      updateGroupApplicationUnread();
    }, onGroupCreated: (String groupID) {
      debugPrint("ChatIMManager onGroupCreated: $groupID, ");
    }, onGroupInfoChanged:
        (String groupID, List<V2TimGroupChangeInfo> changeInfos) {
      debugPrint(
          "ChatIMManager onGroupInfoChanged: $groupID, changeInfos: $changeInfos");
      for (var element in _listeners) {
        element.onGroupInfoChanged?.call(groupID, changeInfos);
      }
      updateConversationList();
    }, onGroupDismissed: (String groupID, V2TimGroupMemberInfo opUser) {
      debugPrint("ChatIMManager onGroupDismissed: $groupID, ");
      for (var element in _listeners) {
        element.onGroupDismissed?.call(groupID, opUser);
      }
    }, onMemberEnter: (String groupID, List<V2TimGroupMemberInfo> memberList) {
      debugPrint("ChatIMManager onMemberEnter: $groupID, ");
      for (var element in _listeners) {
        element.onMemberEnter?.call(groupID, memberList);
      }
      bool needUpdate = memberList
          .map((e) => e.userID == UserService().user?.id)
          .toList()
          .isNotEmpty;
      if (needUpdate) {
        updateUserContactsList();
      }
    }, onMemberLeave: (String groupID, V2TimGroupMemberInfo member) {
      debugPrint("ChatIMManager onMemberLeave: $groupID, ");
      for (var element in _listeners) {
        element.onMemberLeave?.call(groupID, member);
      }
    }, onMemberInvited: (String groupID, V2TimGroupMemberInfo opUser,
        List<V2TimGroupMemberInfo> memberList) {
      debugPrint("ChatIMManager onMemberInvited: $groupID, ");
      for (var element in _listeners) {
        element.onMemberInvited?.call(groupID, opUser, memberList);
      }
    }, onMemberKicked: (String groupID, V2TimGroupMemberInfo opUser,
        List<V2TimGroupMemberInfo> memberList) {
      debugPrint("ChatIMManager onMemberKicked: $groupID, ");
      for (var element in _listeners) {
        element.onMemberKicked?.call(groupID, opUser, memberList);
      }
    }, onGroupAttributeChanged: (
      String groupID,
      Map<String, String> groupAttributeMap,
    ) {
      if (groupAttributeMap.containsKey("seatList")) {
        List list = jsonDecode(groupAttributeMap["seatList"] as String);
        List<ChatRoomSeatInfoEntity> seatList = list
            .map((e) => jsonConvert.convert<ChatRoomSeatInfoEntity>(e)
                as ChatRoomSeatInfoEntity)
            .toList();
        for (var element in _listeners) {
          element.onRoomSeatListUpdated?.call(groupID, seatList);
        }
      }
      if (groupAttributeMap.containsKey("roomTopic")) {
        Map<String, dynamic> map =
            jsonDecode(groupAttributeMap["roomTopic"] as String);
        List list = map["roomTopicList"];
        List<TopicItemEntity> topicList = list
            .map((e) =>
                jsonConvert.convert<TopicItemEntity>(e) as TopicItemEntity)
            .toList();
        for (var element in _listeners) {
          element.onRoomTopicListUpdated?.call(groupID, topicList);
        }
      }
      debugPrint(
          "onGroupAttributeChanged: $groupID, groupAttributeMap: $groupAttributeMap");
    });
    _sdkInstance.addGroupListener(listener: _groupListener);

    ///Conversation Listener
    _conversationListener = V2TimConversationListener(
      onSyncServerStart: () {},
      onSyncServerFinish: () {},
      onSyncServerFailed: () {},
      onNewConversation: (List<V2TimConversation> conversationList) {
        debugPrint("ChatIMManager onNewConversation: $conversationList");
      },
      onConversationChanged: (List<V2TimConversation> conversationList) {
        debugPrint("ChatIMManager onConversationChanged: $conversationList");
        updateConversationList();
      },
      onTotalUnreadMessageCountChanged: (int totalUnreadCount) {
        ///更新TabBar，消息tab 未读总数
        Get.find<MainController>().updateUnreadMessageCount(totalUnreadCount);

        ///更新会话列表
        updateConversationList();

        ///刷新某一个会话的未读消息
        EventBusEngine.fire(event: BusEvent.reloadConversationUnread);
      },
      onConversationGroupCreated:
          (String groupName, List<V2TimConversation> conversationList) {},
      onConversationGroupDeleted: (String groupName) {},
      onConversationsAddedToGroup:
          (String groupName, List<V2TimConversation> conversationList) {},
      onConversationDeleted: (List<String> conversationIDList) {},
    );
    _sdkInstance
        .getConversationManager()
        .addConversationListener(listener: _conversationListener);

    ///好友关系监听器
    _friendshipListener = V2TimFriendshipListener(
      onBlackListAdd: (List<V2TimFriendInfo> infoList) async {
        debugPrint("ChatIMManager onBlackListAdd");
        updateUserContactsList();
      },
      onBlackListDeleted: (List<String> userList) async {
        debugPrint("ChatIMManager onBlackListDeleted");
        updateUserContactsList();
      },
      onFriendApplicationListAdded:
          (List<V2TimFriendApplication> applicationList) async {
        debugPrint("ChatIMManager onFriendApplicationListAdded");
        updateUserContactsList();
        Get.find<MainController>().getChatTabUnreadCount();
      },
      onFriendApplicationListRead: () async {
        //好友请求已读的回调
        debugPrint("ChatIMManager onFriendApplicationListRead");
        Get.find<MainController>().getChatTabUnreadCount();
      },
      onFriendListAdded: (List<V2TimFriendInfo> infoList) {
        debugPrint("ChatIMManager onFriendListAdded");
        updateUserContactsList();
      },
      onFriendListDeleted: (List<String> userList) {
        debugPrint("ChatIMManager onFriendListDeleted");
        updateUserContactsList();
      },
      onFriendInfoChanged: (List<V2TimFriendInfo> infoList) {
        debugPrint("ChatIMManager onFriendInfoChanged");
        updateUserContactsList();
      },
    );
    _sdkInstance
        .getFriendshipManager()
        .addFriendListener(listener: _friendshipListener);
  }

  ///登录腾讯云 IM SDK
  Future<void> login() async {
    if (UserService().user?.id == null) {
      debugPrint("IM登录失败：用户信息为空");
      return;
    }

    if (!imSDKInitSuccess) {
      debugPrint("IM SDK未初始化，开始初始化");
      _initTIMUIKit();
      // 等待初始化完成
      await Future.delayed(const Duration(milliseconds: 500));
      if (!imSDKInitSuccess) {
        debugPrint("IM SDK初始化失败");
        return;
      }
    }

    // 如果已经在登录中，避免重复登录
    if (isLogging) {
      debugPrint("IM正在登录中，跳过重复登录");
      return;
    }

    // 如果已经登录成功，检查是否需要重新登录
    if (imLogined) {
      debugPrint("IM已登录，检查用户是否一致");
      // 可以添加用户ID检查逻辑
      return;
    }

    String? userSig = await getUserSig();
    if (userSig == null) {
      debugPrint("IM登录失败：userSig为空");
      return;
    }

    isLogging = true;
    debugPrint("开始IM登录，用户ID: ${UserService().user!.id!}");

    try {
      V2TimCallback loginRes = await _coreInstance.login(
          userID: UserService().user!.id!, userSig: userSig);

      if (loginRes.code == 0) {
        imLogined = true;
        debugPrint("IM登录成功");

        // 登录成功后的初始化操作
        await setImUserInfo();
        await getTotalUnreadMessageCount();
        await PushManager.sharedInstance.initPush();
      } else {
        imLogined = false;
        debugPrint("IM登录失败，错误码: ${loginRes.code}, 错误信息: ${loginRes.desc}");
      }
    } catch (e) {
      imLogined = false;
      debugPrint("IM登录异常: $e");
    } finally {
      isLogging = false;
    }
  }

  ///获取用户签名(应该由服务器生成，调试阶段用本地代码生成)
  Future<String?> getUserSig() async {
    /*
    /// AppKey 存放在服务端，这里暂时调试使用
    String appKey = AppConfig.imAppKey;
    // 默认时间：7 x 24 x 60 x 60 = 604800 = 7 天
    int expireTime = 604800;
    GenerateDevUserSigForTest generateDevUserSigForTest =
        GenerateDevUserSigForTest(sdkappid: AppConfig.imAppId, key: appKey);
    String userSig = generateDevUserSigForTest.genSig(
        identifier: UserService().user!.id!, expire: expireTime);

     */
    String? sig = UserService().userSig;
    if (sig == null || sig.isEmpty) {
      debugPrint("userSig为空，无法进行IM登录");
      return null;
    }
    _userSig = sig;
    debugPrint("获取到userSig，长度: ${sig.length}");
    return sig;
  }

  ///退出登录
  Future<bool> logout() async {
    if (!imLogined) {
      debugPrint("IM未登录，无需退出");
      return true;
    }

    debugPrint("开始IM退出登录");
    try {
      V2TimCallback result = await _coreInstance.logout();
      if (result.code == 0) {
        imLogined = false;
        _userSig = null;
        debugPrint("IM退出登录成功");
        return true;
      } else {
        debugPrint("IM退出登录失败，错误码: ${result.code}, 错误信息: ${result.desc}");
        // 即使退出失败，也要重置状态
        imLogined = false;
        _userSig = null;
        return false;
      }
    } catch (e) {
      debugPrint("IM退出登录异常: $e");
      // 异常情况下也要重置状态
      imLogined = false;
      _userSig = null;
      return false;
    }
  }

  ///设置用户信息
  Future<void> setImUserInfo(
      {String? userName, String? userAvatar, int? sex}) async {
    UserInfoEntity? user = UserService().user;
    if (user != null) {
      V2TimUserFullInfo userFullInfo = V2TimUserFullInfo(
        userID: user.id,
        nickName: userName ?? user.nickname,
        faceUrl: userAvatar ?? user.avatar,
        gender: sex ?? (user.sex == 0 ? 1 : 2),
        birthday: user.age ?? 0,
        customInfo: {
          "avatar": user.avatarFrame ?? "",
          "dialog": user.chatBubble ?? "",
        },
      );
      V2TimCallback? callback =
          await _sdkInstance.setSelfInfo(userFullInfo: userFullInfo);
      if (callback.code == 0) {
        serviceLocator<TUISelfInfoViewModel>().setLoginInfo(userFullInfo);
        debugPrint("用户信息设置到 IM 成功！");
      }
    }
  }

  ///获取单个用户信息
  Future<V2TimUserFullInfo?> getUserInfo(String userID) async {
    List<V2TimUserFullInfo>? result = await getUserInfoList(userIDs: [userID]);
    return result?.first;
  }

  ///批量获取用户信息
  Future<List<V2TimUserFullInfo>?> getUserInfoList(
      {required List<String> userIDs}) async {
    //获取用户资料
    V2TimValueCallback<List<V2TimUserFullInfo>> getUsersInfoRes =
        await TencentImSDKPlugin.v2TIMManager
            .getUsersInfo(userIDList: userIDs); //需要查询的用户id列表
    if (getUsersInfoRes.code == 0) {
      // 查询成功
      return getUsersInfoRes.data;
    }
    return null;
  }

  // ///批量获取用户在线状态（只有旗舰版支持）
  // Future<List<bool>?> getUserOnlineStatus(
  //     {required List<String> userIds}) async {
  //   //获取用户在线状态
  //   V2TimValueCallback<List<V2TimUserStatus>> getUserStatusRes =
  //       await TencentImSDKPlugin.v2TIMManager
  //           .getUserStatus(userIDList: userIds); // 需要查询用户在线状态的用户id列表
  //   if (getUserStatusRes.code == 0) {
  //     //查询成功
  //     return getUserStatusRes.data?.map((e) => e.statusType == 1).toList();
  //   }
  //   return null;
  // }

  ///-----------------------------------mark------------------------------------
  ///
  /// private method
  ///刷新通讯录
  void updateUserContactsList() {
    if (_lastUpdateContactsListDate != null) {
      int timestamp = DateTime.now()
          .difference(_lastUpdateContactsListDate!)
          .inMilliseconds;
      if (timestamp < 1000) {
        return;
      }
    }
    if (Get.isRegistered<ContactsController>()) {
      Get.find<ContactsController>().loadData(showLoading: false);
    }
    if (Get.isRegistered<PrivateChatSettingController>()) {
      Get.find<PrivateChatSettingController>().loadUserInfo(showLoading: false);
    }
    EventBusEngine.fire(event: BusEvent.reloadChatDetail);
  }

  ///刷新会话列表
  void updateConversationList() {
    if (_lastUpdateConversationListDate != null) {
      int timestamp = DateTime.now()
          .difference(_lastUpdateConversationListDate!)
          .inMilliseconds;
      if (timestamp < 1000) {
        return;
      }
    }
    if (Get.isRegistered<ChatConversationListController>()) {
      Get.find<ChatConversationListController>().refreshData();
    }
  }

  ///聊天内小红点
  void updateGroupApplicationUnread() {
    EventBusEngine.fire(event: BusEvent.receiveGroupApplication);
  }

  ///添加监听
  void addListener(ChatImListener listener) {
    _listeners.add(listener);
  }

  ///添加监听
  void removeListener(ChatImListener listener) {
    _listeners.remove(listener);
  }

  ///移除SDK监听
  void _unRegisterListenerCallback() {
    _sdkInstance
        .getMessageManager()
        .removeAdvancedMsgListener(listener: _advancedMsgListener);
    _sdkInstance.removeGroupListener(listener: _groupListener);
  }

  void _unInit() {
    _userSig = null;
    _coreInstance.unInit();
    _sdkInstance.unInitSDK();
  }

  @override
  void onClose() {
    _unRegisterListenerCallback();
    _unInit();
    _sharedInstance = null;
    super.onClose();
  }
}
