import 'package:dada/model/user_info_entity.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/token_manager.dart';
import 'package:get/get.dart';
import 'package:sp_util/sp_util.dart';

class LoginUtils {
  static bool _isLoggingOut = false; // 防止重复logout

  static bool isLogined() {
    String? token = SpUtil.getString("token");
    UserInfoEntity? infoEntity = UserService().user;
    String? isRegister = SpUtil.getString("isRegister");
    if (token != null && infoEntity?.id != null && isRegister == "1") {
      return true;
    }
    return false;
  }

  static logOut({bool? force}) async {
    // 防止重复调用logout
    if (_isLoggingOut) {
      print('------------------- logout already in progress, skipping');
      return;
    }

    _isLoggingOut = true;
    print('------------------- logout start, force: $force');

    try {
      if (force == true) {
        try {
          // 先清理IM相关状态
          await ChatIMManager.sharedInstance.logout();
          ChatIMManager.sharedInstance.onClose();

          // 清理用户数据
          UserService().removeUser();
          SpUtil.clear();
          GlobalFloatingManager().closeMiniWindow();
          UserService().onClose();

          if (Get.isDialogOpen == true) {
            Get.back();
          }
        } catch (e) {
          print('------------------- logout error ${e.toString()}');
        }
        Get.offAllNamed(GetRouter.welcome);
      } else {
        bool success = await ApiService().logout();
        if (success) {
          GlobalFloatingManager().closeMiniWindow();
          await ChatIMManager.sharedInstance.logout();
          ChatIMManager.sharedInstance.onClose();
          UserService().onClose();
          if (Get.isDialogOpen == true) {
            Get.back();
          }
          Get.offAllNamed(GetRouter.welcome);
        }
      }
    } finally {
      _isLoggingOut = false;
      // 重置token过期处理状态
      TokenManager.resetTokenExpiredHandling();
      print('------------------- logout end');
    }
  }
}
