import 'package:dada/services/user/user_service.dart';
import 'package:sp_util/sp_util.dart';

/// Token管理工具类，用于处理token过期相关逻辑
class TokenManager {
  static bool _isHandlingTokenExpired = false;
  static DateTime? _lastTokenExpiredTime;
  static const int _tokenExpiredCooldownMs = 1000; // 1秒冷却时间

  /// 检查token是否有效
  static bool isTokenValid() {
    String? token = SpUtil.getString("token");
    return token != null && token.isNotEmpty;
  }

  /// 处理token过期，防止重复处理
  static bool handleTokenExpired() {
    DateTime now = DateTime.now();

    // 检查是否在冷却时间内
    if (_lastTokenExpiredTime != null) {
      int timeDiff = now.millisecondsSinceEpoch -
          _lastTokenExpiredTime!.millisecondsSinceEpoch;
      if (timeDiff < _tokenExpiredCooldownMs) {
        print(
            'TokenManager: 在冷却时间内，跳过处理 (${timeDiff}ms < ${_tokenExpiredCooldownMs}ms)');
        return false;
      }
    }

    if (_isHandlingTokenExpired) {
      print('TokenManager: 已经在处理token过期，跳过重复处理');
      return false; // 已经在处理中，返回false表示不需要重复处理
    }

    _isHandlingTokenExpired = true;
    _lastTokenExpiredTime = now;
    print('TokenManager: 开始处理token过期');
    return true; // 返回true表示可以处理
  }

  /// 重置token过期处理状态
  static void resetTokenExpiredHandling() {
    _isHandlingTokenExpired = false;
    print('TokenManager: 重置token过期处理状态');
  }

  /// 清理所有token相关数据
  static void clearTokenData() {
    SpUtil.remove("token");
    SpUtil.remove("isRegister");
    UserService().userSig = null;
    _isHandlingTokenExpired = false;
    _lastTokenExpiredTime = null;
    print('TokenManager: 清理所有token相关数据');
  }
}
