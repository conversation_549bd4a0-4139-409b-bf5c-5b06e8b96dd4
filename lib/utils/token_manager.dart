import 'package:dada/services/user/user_service.dart';
import 'package:sp_util/sp_util.dart';

/// Token管理工具类，用于处理token过期相关逻辑
class TokenManager {
  static bool _isHandlingTokenExpired = false;
  
  /// 检查token是否有效
  static bool isTokenValid() {
    String? token = SpUtil.getString("token");
    return token != null && token.isNotEmpty;
  }
  
  /// 处理token过期，防止重复处理
  static bool handleTokenExpired() {
    if (_isHandlingTokenExpired) {
      return false; // 已经在处理中，返回false表示不需要重复处理
    }
    
    _isHandlingTokenExpired = true;
    return true; // 返回true表示可以处理
  }
  
  /// 重置token过期处理状态
  static void resetTokenExpiredHandling() {
    _isHandlingTokenExpired = false;
  }
  
  /// 清理所有token相关数据
  static void clearTokenData() {
    SpUtil.remove("token");
    SpUtil.remove("isRegister");
    UserService().userSig = null;
    _isHandlingTokenExpired = false;
  }
}
