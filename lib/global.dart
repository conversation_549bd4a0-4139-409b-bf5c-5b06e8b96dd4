import 'dart:async';

import 'package:dada/services/ad/ad_plugin_manager.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/prop/prop_manager.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/emoji_utils.dart';
import 'package:dada/utils/login_utils.dart';
import 'package:sp_util/sp_util.dart';
import 'package:dada/services/network/api.dart';
import 'package:dada/services/network/http_utils.dart';

class Global {
  static Future init() async {
    Stopwatch stopwatch = Stopwatch();
    stopwatch.start();

    var environment = const String.fromEnvironment('env', defaultValue: 'prod');
    ApiConfig.env = Env.values.firstWhere(
        (type) => type.toString().split('.').last == environment,
        orElse: () => Env.dev);

    ///请求设置
    HttpUtil.addLog();

    ///服务注入
    await initServices();
  }

  static Future<void> initServices() async {
    ///添加网络状态监测
    // addNetworkMonitor();

    ///ApiService
    ApiService.init();

    ///本地存储
    await SpUtil.getInstance();

    ///UserService
    UserService.init();

    ///全局悬浮窗管理类
    GlobalFloatingManager.init();
  }

  static void initServiceAfterLogin() async {
    ///确保用户信息和token都存在
    if (!LoginUtils.isLogined()) {
      print('initServiceAfterLogin: 用户未登录，跳过初始化');
      return;
    }

    print('initServiceAfterLogin: 开始登录后服务初始化');

    ///Prop
    PropManager.init();

    ///表情资源加载
    EmojiUtils.init();

    ///IM SDK - 延迟初始化，确保用户信息已经设置完成
    Future.delayed(const Duration(milliseconds: 300), () {
      if (LoginUtils.isLogined()) {
        print('initServiceAfterLogin: 开始IM登录');
        ChatIMManager.sharedInstance.login();
      } else {
        print('initServiceAfterLogin: 用户状态已变更，跳过IM登录');
      }
    });
  }

  // static void addNetworkMonitor() {
  //   StreamSubscription<List<ConnectivityResult>> subscription = Connectivity()
  //       .onConnectivityChanged
  //       .listen((List<ConnectivityResult> result) {
  //     // Received changes in available connectivity types!
  //   });
  // }
}
