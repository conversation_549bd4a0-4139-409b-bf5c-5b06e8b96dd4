import 'package:carousel_slider/carousel_slider.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/badge_widget.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/say_hi_dialog.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/home/<USER>';
import 'package:dada/pages/todo_together/dialog/todo_together_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/trtc/chat_room_rtc_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_infinite_marquee/flutter_infinite_marquee.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:showcaseview/showcaseview.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final controller = Get.put(HomeController());
  int messageIndex = 0;

  @override
  void initState() {
    super.initState();
    controller.data = controller.initData;
  }

  @override
  void dispose() {
    ShowcaseView.getNamed("home_page").dismiss();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      body: Stack(
        children: [
          ///背景
          ImageUtils.getImage(Assets.imagesHomeBack, ScreenUtil().screenWidth,
              ScreenUtil().screenHeight,
              fit: BoxFit.cover),
          Container(
            padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 13.h),
            child: Column(
              children: [
                ///搜索
                Padding(
                  padding: EdgeInsets.only(left: 16.w, right: 13.w),
                  child: _buildSearch(),
                ),

                _buildMessage(),

                Expanded(
                  child: CustomScrollView(
                    controller: ScrollController(),
                    slivers: [
                      ///顶部卡片
                      SliverToBoxAdapter(
                        child: _buildHeadCard(),
                      ),

                      ///表头
                      /* SliverToBoxAdapter(
                        child: _buildListHeader(),
                      ), */

                      ///推荐列表
                      SliverPadding(
                        padding: EdgeInsets.only(
                            top: 8.h, left: 15.w, right: 15.w, bottom: 20.h),
                        sliver: _buildList(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearch() {
    return Row(
      children: [
        ImageUtils.getImage(Assets.imagesHomeDayaTitle, 40.w, 25.h),
        Spacer(),

        ///搜索框
        /* GestureDetector(
          onTap: () {
            Get.toNamed(GetRouter.searchRoom);
          },
          child: Container(
            width: 120.w,
            height: 30.h,
            margin: EdgeInsets.only(left: 22.w),
            padding: EdgeInsets.only(left: 10.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              color: Colors.white.withOpacity(0.23),
            ),
            child: Row(
              children: [
                ImageUtils.getImage(
                    Assets.imagesHomeSearchIcon1, 13.33.w, 13.33.w),
                SizedBox(width: 8.w),
                Text(
                  "房间号/关键词",
                  style: TextStyles.normal(12.sp, c: Colors.white),
                ),
              ],
            ),
          ),
        ),

        SizedBox(width: 9.w),

        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            Get.toNamed(GetRouter.chatRoomList);
          },
          child: Row(
            children: [
              Text(
                "房间列表",
                style: TextStyles.common(12.sp, Colors.white),
              ),
              SizedBox(width: 3.w),
              ImageUtils.getImage(Assets.imagesCommonListMore, 5.w, 8.h,
                  color: Colors.white),
            ],
          ),
        ), */

        /*const Spacer(),

         ///签到
        GestureDetector(
          onTap: () {
            controller.showSignInDialog();
          },
          child: Container(
            margin: EdgeInsets.only(right: 10.w),
            width: 78.w,
            height: 35.h,
            padding: EdgeInsets.only(left: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(35.h / 2),
              color: const Color(0xFF7DB37C),
            ),
            child: Row(
              children: [
                ImageUtils.getImage(Assets.imagesHomeSignIcon, 24.w, 24.w),
                SizedBox(width: 7.w),
                Text(
                  S.current.sign,
                  style: TextStyles.normal(14.sp,
                      c: AppTheme.themeData.colorScheme.primary),
                ),
              ],
            ),
          ),
        ), */
      ],
    );
  }

  ///消息喇叭
  Widget _buildMessage() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 13.w, vertical: 7.h),
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      height: 36.h,
      decoration: BoxDecoration(
          color: Color(0xFFE9E6E6).withOpacity(0.4),
          borderRadius: BorderRadius.circular(8.r)),
      child: Row(
        children: [
          ImageUtils.getImage(Assets.imagesHomeMessageIcon, 18, 18),
          SizedBox(width: 2.w),
          SizedBox(
            width: ScreenUtil().screenWidth - 26.w - 24.w - 18 - 2.w,
            height: 36.h,
            child: InfiniteMarquee(
                scrollDirection: Axis.vertical,
                stepOffset: 36.h,
                frequency: const Duration(seconds: 5),
                separatorBuilder: (context, index) {
                  return const SizedBox();
                },
                itemBuilder: (_, i) {
                  if (messageIndex > controller.messageList.length - 1) {
                    messageIndex = 0;
                  }
                  String message = controller.messageList[messageIndex];
                  messageIndex++;
                  return Container(
                    alignment: Alignment.centerLeft,
                    height: 36.h,
                    child: Text(
                      message,
                      style: TextStyles.common(12.sp, const Color(0xFFFAFAFA)),
                    ),
                  );
                }),
          )
        ],
      ),
    );
  }

  Widget _buildHeadCard() {
    return Container(
      margin: EdgeInsets.only(left: 1.w, right: 13.w),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///一起做件事
              Obx(() => BadgeWidget(
                  offset: Offset(1, 0),
                  text: controller.todoTogetherUnreadId.value.isNotEmpty
                      ? "1"
                      : null,
                  child: controller.hasShow
                      ? GestureDetector(
                          onTap: () {
                            Get.toNamed(GetRouter.todoTogether);
                          },
                          child: ImageUtils.getImage(
                              Assets.imagesHomeTodoTogether2, 192.w, 150.h),
                        )
                      : Showcase(
                          key: controller.one,
                          description: "在无拘无束中结识与自己合拍的人！不想和人尬聊？来试试这里吧！~",
                          child: GestureDetector(
                            onTap: () {
                              Get.toNamed(GetRouter.todoTogether);
                            },
                            child: ImageUtils.getImage(
                                Assets.imagesHomeTodoTogether2, 192.w, 149.33.h,
                                fit: BoxFit.cover),
                          ),
                        ))),
              const Spacer(),

              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ///扩列
                  GestureDetector(
                    onTap: () {
                      controller.sendGreetMsg();
                    },
                    child: ImageUtils.getImage(
                        Assets.imagesHomeKuoLie, 169.w, 80.h,
                        fit: BoxFit.cover),
                  ),
                  /* ///游戏开黑匹配
                  GestureDetector(
                    onTap: () {
                      Get.toNamed(GetRouter.matchAssemblePlace);
                    },
                    child: ImageUtils.getImage(
                        Assets.imagesHomeKaiheiGame, 169.w, 80.h,
                        fit: BoxFit.cover),
                  ), */

                  ///世界聊天室
                  SizedBox(
                    width: 167.w,
                    height: 68.h,
                    child: Stack(
                      children: [
                        CarouselSlider(
                          carouselController: controller.carouselController,
                          options: CarouselOptions(
                            height: 68.h,
                            aspectRatio: 167.w / 68.h,
                            viewportFraction: 1,
                          ),
                          items: [1, 2]
                              .map(
                                (e) => GestureDetector(
                                  onTap: () {
                                    controller.gotoWorldGroupChat(e);
                                  },
                                  child: SizedBox(
                                    width: 167.w,
                                    height: 68.h,
                                    child: ImageUtils.getImage(
                                      e == 1
                                          ? Assets.imagesHomeWordChatRoom
                                          : Assets.imagesHomeWorldChatCard2,
                                      167.w,
                                      68.h,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                        Positioned(
                          right: 0,
                          bottom: 0,
                          width: 28.w,
                          height: 100.h,
                          child: GestureDetector(
                            onTap: () {
                              controller.carouselController.nextPage();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  /* ///去茶壶
                  GestureDetector(
                    onTap: () async {
                      if (GlobalFloatingManager().currentIsShowMiniWindow()) {
                        return;
                      }
                      TRTCManager.sharedInstance.resetMicAndSpeakerState();
                      ChatRoomInfoEntity? roomInfo =
                          await ApiService().getRandomMatchingRoom(roomType: 2);
                      if (roomInfo != null) {
                        Get.toNamed(
                          GetRouter.chatRoomDetail,
                          parameters: {
                            "roomId": roomInfo.roomNo!,
                            "roomType": "2"
                          },
                        );
                      }
                    },
                    child: Container(
                      width: 165.w,
                      height: 120.h,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(Assets.imagesHomeHeadCardTeapot),
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            left: 9.w,
                            bottom: 5.h,
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                if (GlobalFloatingManager()
                                    .currentIsShowMiniWindow()) {
                                  return;
                                }
                                Get.toNamed(GetRouter.createRoom,
                                    arguments: ChatRoomType.teapot);
                              },
                              child: SizedBox(width: 56.w, height: 42.h),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ), 

                  SizedBox(
                    height: 10.h,
                  ),

                  ///去市集
                  GestureDetector(
                    onTap: () async {
                      if (GlobalFloatingManager().currentIsShowMiniWindow()) {
                        return;
                      }
                      TRTCManager.sharedInstance.resetMicAndSpeakerState();
                      ChatRoomInfoEntity? roomInfo =
                          await ApiService().getRandomMatchingRoom(roomType: 1);
                      if (roomInfo != null) {
                        Get.toNamed(
                          GetRouter.chatRoomDetail,
                          parameters: {
                            "roomId": roomInfo.roomNo!,
                            "roomType": "1"
                          },
                        );
                      }
                    },
                    child: Container(
                      width: 165.w,
                      height: 55.h,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(Assets.imagesHomeHeadCardFairSmall),
                        ),
                      ),
                      child: Stack(
                        alignment: Alignment.centerLeft,
                        children: [
                          Positioned(
                            left: 10.w,
                            child: GestureDetector(
                              onTap: () {
                                if (GlobalFloatingManager()
                                    .currentIsShowMiniWindow()) {
                                  return;
                                }
                                Get.toNamed(GetRouter.createRoom,
                                    arguments: ChatRoomType.fair);
                              },
                              child: ImageUtils.getImage(
                                  Assets.imagesHomeHeadFairCardSmallCreateRoom,
                                  40.w,
                                  40.w),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),

                  ///扩列
                  GestureDetector(
                    onTap: () {
                      controller.sendGreetMsg();
                    },
                    child: Container(
                      width: 165.w,
                      height: 55.h,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(Assets.imagesHomeHeadSuiji),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: Container(),
                    ),
                  ),*/
                ],
              ),
            ],
          ),
          /* SizedBox(height: 9.h),
          Padding(
            padding: EdgeInsets.only(left: 10.w),
            child: Row(
              children: [
                ///随机匹配聊天
                GestureDetector(
                  onTap: () {
                    controller.sendGreetMsg();
                  },
                  child: ImageUtils.getImage(
                      Assets.imagesHomeChatRandom, 116.w, 58.h,
                      fit: BoxFit.cover),
                ),

                ///语音聊天室
                Stack(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.toNamed(GetRouter.chatRoomList);
                      },
                      child: ImageUtils.getImage(
                          Assets.imagesHomeVoiceChatRoom, 116.w, 56.h,
                          fit: BoxFit.cover),
                    ),
                    Positioned(
                        bottom: 0.h,
                        left: 12.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            if (GlobalFloatingManager()
                                .currentIsShowMiniWindow()) {
                              return;
                            }
                            Get.toNamed(GetRouter.createRoom,
                                arguments: ChatRoomType.teapot);
                          },
                          child: const SizedBox(width: 30, height: 30),
                        ))
                  ],
                ),

                ///随机语音房
                Stack(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.toNamed(GetRouter.chatRoomList);
                      },
                      child: ImageUtils.getImage(
                          Assets.imagesHomeVoiceRoomRandom, 116.w, 56.h,
                          fit: BoxFit.cover),
                    ),
                    Positioned(
                        bottom: 0.h,
                        left: 12.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            if (GlobalFloatingManager()
                                .currentIsShowMiniWindow()) {
                              return;
                            }
                            Get.toNamed(GetRouter.createRoom,
                                arguments: ChatRoomType.fair);
                          },
                          child: const SizedBox(width: 30, height: 30),
                        ))
                  ],
                ),
              ],
            ),
          ), */
          SizedBox(height: 7.h),
          _buildBanner(),
          /* Padding(
            padding: EdgeInsets.only(top: 17.h),
            child: Row(
              children: [
                _buildMatchTeamCard(),
                _buildWorldChatCard(),
              ],
            ),
          ), */
        ],
      ),
    );
  }

  Widget _buildBanner() {
    return ImageUtils.getImage(Assets.imagesHomeBanner1, 348.69.w, 79.h,
        fit: BoxFit.cover);
    /* return Obx(() {
      return Container(
        margin: EdgeInsets.only(top: 17.h),
        height: 80.h,
        decoration: BoxDecoration(
          image: controller.bannerList.isNotEmpty == true
              ? null
              : const DecorationImage(
                  image: AssetImage(Assets.imagesHomeBanner),
                ),
          color: AppColors.colorFFE1E1E1,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: CarouselSlider(
          options: CarouselOptions(
              height: 80.h,
              aspectRatio: (ScreenUtil().screenWidth - 15.w * 2) / 80.h,
              viewportFraction: 1.05,
              autoPlayInterval: const Duration(seconds: 7),
              autoPlay: controller.bannerList.length > 1),
          items: controller.bannerList.map((e) {
            int index = controller.bannerList.indexOf(e);
            return GestureDetector(
              onTap: () {},
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.r),
                child: ImageUtils.getImage(
                  e.url ?? "",
                  ScreenUtil().screenWidth - 15.w * 2,
                  80.h,
                  fit: BoxFit.cover,
                  placeholder: index == 0 ? Assets.imagesHomeBanner1 : null,
                  showPlaceholder: index == 0,
                ),
              ),
            );
          }).toList(),
        ),
      );
    }); */
  }

  /* Widget _buildMatchTeamCard() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(GetRouter.matchAssemblePlace);
      },
      child: SizedBox(
        width: 165.w,
        height: 60.h,
        child: ImageUtils.getImage(Assets.imagesHomeMatchCard, 165.w, 60.h,
            fit: BoxFit.fill),
      ),
    );
  }

  Widget _buildWorldChatCard() {
    return Container(
      width: 165.w,
      height: 60.h,
      margin: EdgeInsets.only(left: 12.w),
      child: Stack(
        children: [
          CarouselSlider(
            carouselController: controller.carouselController,
            options: CarouselOptions(
              height: 60.h,
              aspectRatio: 165.w / 60.h,
              viewportFraction: 1,
            ),
            items: [1, 2]
                .map(
                  (e) => GestureDetector(
                    onTap: () {
                      controller.gotoWorldGroupChat(e);
                    },
                    child: SizedBox(
                      width: 165.w,
                      height: 60.h,
                      child: ImageUtils.getImage(
                        e == 1
                            ? Assets.imagesHomeWorldChatCard1
                            : Assets.imagesHomeWorldChatCard2,
                        165.w,
                        60.h,
                        fit: BoxFit.fill,
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            width: 28.w,
            height: 100.h,
            child: GestureDetector(
              onTap: () {
                controller.carouselController.nextPage();
              },
            ),
          ),
        ],
      ),
    );
  } */

  /* Widget _buildListHeader() {
    return Container(
      margin: EdgeInsets.only(top: 17.h, left: 15.w),
      child: Text(
        "发现新伙伴",
        style: TextStyles.common(17.sp, AppColors.colorFF2D6D0B,
            w: FontWeight.w600),
      ),
    );
  } */

  Widget _buildList() {
    return GetBuilder<HomeController>(
      init: controller,
      builder: (controller) {
        if (controller.data.isEmpty) {
          return SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsets.only(top: 30.h),
              child: EmptyWidget(
                content: "暂无内容，点击刷新试试~",
                onTap: () {
                  controller.checkShouldReload();
                },
              ),
            ),
          );
        }
        return SliverList.separated(
          itemCount: controller.data.length,
          separatorBuilder: (context, index) {
            return Container(
              height: 10.h,
              color: Colors.transparent,
            );
          },
          itemBuilder: (context, index) {
            // UserInfoEntity itemEntity = controller.data[index];
            TodoTogetherListEntity itemEntity = controller.data[index];
            return _buildListItem(itemEntity);
          },
        );
      },
    );
  }

  Widget _buildListItem(TodoTogetherListEntity model) {
    String bacImg = model.type == 0
        ? Assets.imagesTodoTogetherListBacOther
        : Assets.imagesTodoTogetherListBac;
    String typeImg = model.type == 1
        ? Assets.imagesTodoTogetherListMyCreate
        : Assets.imagesTodoTogetherListMyJoin;
    String status = model.state == 1
        ? '等待进入...'
        : (model.state == 2 || model.state == 3 ? '进行中...' : '已完成');
    String? statusBtnImg;

    if (model.type == 1) {
      if (model.state == 1) {
        statusBtnImg = Assets.imagesTodoTogetherListDelete;
      } else if (model.state == 2 || model.state == 3) {
        statusBtnImg = Assets.imagesTodoTogetherListContinue;
      } else {
        statusBtnImg = null;
      }
    } else if (model.type == 2) {
      if (model.state == 2 || model.state == 3) {
        statusBtnImg = Assets.imagesTodoTogetherListContinue;
      } else {
        statusBtnImg = null;
      }
    } else {
      if (model.state == 1) {
        statusBtnImg = Assets.imagesTodoTogetherListLook;
      } else {
        statusBtnImg = null;
      }
    }

    return Obx(() => BadgeWidget(
          offset: Offset(-15, 5),
          text: controller.todoTogetherUnreadId.value == model.userTaskId
              ? "1"
              : null,
          child: GestureDetector(
            onTap: () {},
            child: SizedBox(
              child: Stack(
                children: [
                  Image.asset(bacImg, width: 343.w, fit: BoxFit.fitWidth),
                  Visibility(
                    visible: model.type != 0,
                    child: Positioned(
                        right: 0,
                        top: 3.h,
                        child: Image.asset(
                          typeImg,
                          width: 92.w,
                          fit: BoxFit.fitWidth,
                        )),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 12.w),
                    child: Row(
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          height: 35.h + 1,
                          child: Text('我想找个人一起',
                              style: TextStyles.medium(14.sp,
                                  c: AppColors.colorFF686868)),
                        ),
                        SizedBox(width: 10.w),
                        Image.asset(
                            model.taskSex == 0
                                ? Assets.imagesTodoTogetherMale
                                : Assets.imagesTodoTogetherFemale,
                            width: model.taskSex == 0 ? 10.4.w : 9.w,
                            height: model.taskSex == 0 ? 11.h : 13.h),
                        SizedBox(width: 2.w),
                        Text(
                          model.sex == 2 ? '不限' : '限',
                          style:
                              TextStyles.common(12.sp, AppColors.colorFFA4A3AD),
                        ),
                        SizedBox(width: 2.w),
                        Visibility(
                            visible: model.sex == 0,
                            child: Image.asset(Assets.imagesTodoTogetherMale,
                                width: 10.4.w, height: 11.h)),
                        SizedBox(width: 2.w),
                        Visibility(
                            visible: model.sex == 1,
                            child: Image.asset(Assets.imagesTodoTogetherFemale,
                                width: 9.w, height: 13.h)),
                        SizedBox(width: 2.w),
                        Visibility(
                            visible: model.sex == 2,
                            child: Image.asset(
                                Assets.imagesTodoTogetherMaleFemale,
                                width: 13.w,
                                height: 11.h)),
                        SizedBox(width: 10.w),
                        Text(
                          status,
                          style:
                              TextStyles.common(12.sp, AppColors.colorFFA4A3AD),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    top: 35.h + 8.h,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Image.asset(Assets.imagesTodoTogetherListBubble,
                            width: 208.w, fit: BoxFit.fitWidth),
                        Container(
                          width: 180.w,
                          padding: EdgeInsets.symmetric(horizontal: 5.w),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            physics: model.content != null &&
                                    model.content!.length > 15
                                ? ClampingScrollPhysics()
                                : NeverScrollableScrollPhysics(),
                            child: Text(
                              model.content ?? '',
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              style: TextStyles.medium(14.sp,
                                  c: AppColors.colorFF686868),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (statusBtnImg != null)
                    Positioned(
                        top: 35.h + 12.h,
                        right: 8.w,
                        child: Row(
                          children: [
                            IconButton(
                                onPressed: () => _pushToDetail(model),
                                icon: Image.asset(
                                  statusBtnImg,
                                  width: 69.w,
                                  fit: BoxFit.fitWidth,
                                ))
                          ],
                        )),
                ],
              ),
            ),
          ),
        ));
    /* return Container(
      height: 100.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32.r),
          topRight: Radius.circular(8.r),
          bottomLeft: Radius.circular(8.r),
          bottomRight: Radius.circular(8.r),
        ),
        gradient: const LinearGradient(
            colors: [Color(0xFFFFFFFF), Color(0xFFEFF4FF)],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight),
        /* image: DecorationImage(
          image: AssetImage(Assets.imagesHomeListItemBg),
          fit: BoxFit.cover,
        ), */
      ),
      child: Stack(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 15.w, top: 12.h),
                child: GestureDetector(
                  onTap: () {
                    if (itemEntity.id?.isNotEmpty == true) {
                      Get.toNamed(GetRouter.userProfile,
                          parameters: {"userId": itemEntity.id!});
                    }
                  },
                  child: ClipOval(
                    child: Container(
                      width: 60.w,
                      height: 60.w,
                      color: AppColors.colorFFE1E1E1,
                      child: ImageUtils.getImage(
                        itemEntity.avatar ?? "",
                        60.w,
                        60.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 10.w, top: 15.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          itemEntity.nickname ?? "-",
                          style: TextStyles.medium(16.sp),
                        ),
                        SizedBox(width: 4.w),
                        ImageUtils.getImage(
                            itemEntity.sex == 0
                                ? Assets.imagesHomeMaleIcon
                                : Assets.imagesHomeFemaleIcon,
                            itemEntity.sex == 0 ? 10.41.w : 8.w,
                            itemEntity.sex == 0 ? 11.12.h : 12.h,
                            fit: BoxFit.cover)
                      ],
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 3.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          /* Text(
                            itemEntity.sex == null
                                ? "-"
                                : (itemEntity.sex == 0 ? "男" : "女"),
                            style: TextStyles.medium(12.sp,
                                c: AppColors.colorFF716D9C),
                          ), */
                          Visibility(
                            visible: itemEntity.age != null,
                            child: Text(
                              "${itemEntity.age}岁",
                              style: TextStyles.medium(10.sp,
                                  c: const Color(0xFF969696)),
                            ),
                          ),
                          Visibility(
                            visible:
                                itemEntity.constellation?.isNotEmpty == true,
                            child: Text(
                              " | ${itemEntity.constellation}",
                              style: TextStyles.medium(10.sp,
                                  c: const Color(0xFF969696)),
                            ),
                          ),
                          Visibility(
                            visible:
                                _getUserRelationState(itemEntity.socialState)
                                        .isNotEmpty ==
                                    true,
                            child: Text(
                              " | ${_getUserRelationState(itemEntity.socialState)}",
                              style: TextStyles.medium(10.sp,
                                  c: const Color(0xFF969696)),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Container(
                        height: 20.h,
                        constraints: BoxConstraints(
                          maxWidth: 170.w,
                        ),
                        child: itemEntity.labels?.isNotEmpty == true
                            ? ListView(
                                padding: EdgeInsets.zero,
                                scrollDirection: Axis.horizontal,
                                children: [
                                  Wrap(
                                    spacing: 5.w,
                                    children: itemEntity.labels!
                                        .map(
                                          (e) => LabelItemWidget(
                                            height: 20.h,
                                            minWidth: 50.w,
                                            editable: false,
                                            borderRadius: 10.r,
                                            text: e.labelName ?? "",
                                            bgColor: const Color(0xFFD9D9D9)
                                                .withOpacity(0.25),
                                            textColor: const Color(0xFF969696),
                                            fontSize: 10.sp,
                                          ),
                                        )
                                        .toList(),
                                  ),
                                ],
                              )
                            : Container(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            right: 10.w,
            // top: 24.h,
            height: 100.h,
            child: GestureDetector(
              onTap: () {
                // if (itemEntity.id?.isNotEmpty == true) {
                //   controller.gotoChat(itemEntity.id!);
                // }
                /// 20250701“去聊天”改为“打招呼”，并修改逻辑，点击弹出打招呼dialog可以向对方发送一句话
                showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (_) {
                      return SayHiDialog(userId: itemEntity.id!);
                    });
              },
              child: ImageUtils.getImage(Assets.imagesHomeToSayHi, 60.w, 25.h,
                  fit: BoxFit.contain),
            ),
          ),
        ],
      ),
    ); */
  }

  void _pushToDetail(TodoTogetherListEntity model) {
    if (controller.todoTogetherUnreadId.value == model.userTaskId) {
      controller.todoTogetherUnreadId.value = "";
    }
    if (model.type == 1) {
      if (model.state == 1) {
        showDialog(
            context: context,
            builder: (_) {
              return TodoTogetherDialog(
                content: '确定要删除吗?',
                onRightBtnTap: () {
                  controller.delete(model.userTaskId);
                },
                onLeftBtnTap: () {
                  Get.back();
                },
                rightBtnTitle: '删除',
                leftBtnTitle: '取消',
              );
            });
      } else {
        controller.toDetail(model.userTaskId, false, model);
      }
    } else if (model.type == 2) {
      controller.toDetail(model.userTaskId, false, model);
    } else {
      if (model.state == 1) {
        controller.toDetail(model.userTaskId, true, model);
      }
    }
  }

  /* String _getUserRelationState(int? state) {
    switch (state) {
      case 1:
        return "冰冷";
      case 2:
        return "慢热";
      case 3:
        return "适中";
      case 4:
        return "热情";
      case 5:
        return "非常热情";
      default:
        return "";
    }
  } */
}
