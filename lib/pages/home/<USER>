import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:carousel_slider/carousel_controller.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/components/controller/base_page_controller.dart';
import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/banner_entity.dart';
import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/dada_match_result_entity.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/home/<USER>/sign_in_dialog.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_callback.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:sp_util/sp_util.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class HomeController extends BasePageController {
  static const kSignInDialogShownDate = "SignInDialogShownDate";
  CarouselSliderController carouselController = CarouselSliderController();

  // List<UserInfoEntity> data = [];
  // List<UserInfoEntity> initData = [UserInfoEntity(), UserInfoEntity()];
  List<TodoTogetherListEntity> data = [];
  List<TodoTogetherListEntity> initData = [
    TodoTogetherListEntity(),
    TodoTogetherListEntity()
  ];

  // RxList<BannerEntity> bannerList = <BannerEntity>[].obs;

  RxString todoTogetherUnreadId = "".obs;

  late StreamSubscription myEventSub;

  final bool hasShow = SpUtil.getBool("showcase_together") ?? false;

  List<String> worldGroupChatIds = [
    "@TGS#3TYYDPLQZ",
    "@TGS#a3E42WLQB",
  ];

  List<String> worldGroupChatNotices = [
    "为了维护良好的聊天室环境，请大家注意网络礼仪，文明规范用语哦~",
    //1.近期热点
    "为了维护良好的聊天室环境，请大家注意网络礼仪，文明规范用语哦~",
    //2.八卦杂谈
  ];

  int retryTimes = 0;

  final GlobalKey one = GlobalKey();

  List<String> messageList = [
    '搭吖”严格保护用户信息，全力用心做功能！',
    '官方QQ群：1050105332，对我们有任何建议、意见欢迎来一起讨论哦！',
    '邀请3人来应用，即可获得“梦舞月华”时装及“传火者”等奖励哦，传火者会有后续丰厚奖励！',
  ];

  @override
  void onInit() {
    super.onInit();
    ShowcaseView.register(scope: "home_page");
    myEventSub = EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.receiveTodoTogetherMsg) {
        todoTogetherUnreadId.value = entity.message ?? "";
        loadListData();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();

    _checkShowSignInDialog();

    loadListData();
    // await _checkShowSignInDialog();

    // Future.delayed(const Duration(milliseconds: 500), () async {
    // });
  }

  @override
  void onClose() {
    myEventSub.cancel();
    super.onClose();
  }

  void loadListData() async {
    if (retryTimes >= 3) {
      return;
    }
    List<TodoTogetherListEntity>? tmpData = await loadData(1);
    if (tmpData?.isNotEmpty == true) {
      data = tmpData!;
      retryTimes = 0;
      update();
    } else {
      retryTimes++;
      loadListData();
    }
  }

  void checkShouldReload() {
    if (data.isEmpty) {
      retryTimes = 0;
      loadListData();
    }
  }

  Future<List<TodoTogetherListEntity>?> loadData(int page) async {
    /* DadaMatchResultEntity? result = await ApiService().getMatchDadaList(
        type: -1, labels: [], subLabels: [], showLoading: false);
    // bannerList.value = await ApiService().getBannerList() ?? [];
    return result?.matchDada; */
    List<TodoTogetherListEntity>? list = await ApiService()
        .getTodoTogetherList(pageIndex: 1, limit: 5, state: "1", content: "");
    return list;
  }

  Future<void> _checkShowSignInDialog() async {
    String? lastShowDate = SpUtil.getString(kSignInDialogShownDate);

    ///还需再判断一下今天是否签到，如果已签到就不弹了；未签到一天只弹一次
    bool todayShown = false;
    if (lastShowDate != null && lastShowDate.isNotEmpty) {
      DateTime date = DateTime.parse(lastShowDate);
      todayShown = TimeUtils.isToday(date);
    }
    if (!todayShown) {
      await showSignInDialog();

      ///记录签到弹窗弹出时间，退出登录会清掉
      SpUtil.putString(kSignInDialogShownDate, DateTime.now().toString());
    }
    if (!hasShow) {
      ShowcaseView.getNamed('home_page')
          .startShowCase([one], delay: const Duration(milliseconds: 200));
    }
  }

  Future<void> showSignInDialog() async {
    await ToastUtils.showDialog(
      dialog: const SignInDialog(),
    );
  }

  void sendGreetMsg() async {
    // 在这里添加点击事件的处理逻辑
    DadaMatchResultEntity? result = await ApiService()
        .getMatchDadaList(type: -1, labels: [], subLabels: []);
    if (result != null && result.matchDada!.isNotEmpty == true) {
      int randomIndex = Random().nextInt(result.matchDada!.length - 1);
      UserInfoEntity userInfoEntity = result.matchDada![randomIndex];
      /* V2TimMessage? message = await ChatIMManager.sharedInstance
          .sendTextMessage(
              text: getRandomGreetMsg(), toUserID: userInfoEntity.id);
      await ChatIMManager.sharedInstance.sendCustomMessage(
          type: ChatImCustomMsgType.ChatGreetTipsMsg,
          data: jsonEncode({"text": "以上打招呼为系统默认发出，搭吖觉得你们很搭呀，互相认识一下吧？^_^"}),
          receiver: userInfoEntity.id!,
          groupID: "");
      V2TimConversation conversation = await ChatIMManager.sharedInstance
          .getConversation(userID: userInfoEntity.id, type: 1);
      if (message != null) {
        Get.toNamed(GetRouter.chatDetail, arguments: conversation);
      } */
      Get.toNamed(GetRouter.userProfile,
          parameters: {"userId": userInfoEntity.id!});
    }
  }

  void gotoChat(String userId) async {
    if (!UserService().checkIsMonthCardUser()) {
      ToastUtils.showBottomDialog(const MonthCardExpiredLimitChatDialog());
    } else {
      V2TimConversation conversation = await ChatIMManager.sharedInstance
          .getConversation(userID: userId, type: 1);
      Get.toNamed(GetRouter.chatDetail, arguments: conversation);
    }
  }

  void gotoWorldGroupChat(int index) async {
    if (index == 1) {
      ToastUtils.showLoading();
      V2TimGroupInfo? groupInfo =
          await ChatIMManager.sharedInstance.getGroupInfo(worldGroupChatIds[0]);
      if (groupInfo != null &&
          groupInfo.memberCount != null &&
          groupInfo.memberMaxCount != null &&
          groupInfo.memberCount! < groupInfo.memberMaxCount!) {
        bool success = await ChatIMManager.sharedInstance.joinGroup(
            groupType: GroupType.Meeting,
            groupID: groupInfo.groupID,
            message: "");
        if (!success) {
          ToastUtils.hideLoading();
          ToastUtils.showToast("加入世界聊天群失败，请稍后重试");
        } else {
          V2TimConversation conversation = await ChatIMManager.sharedInstance
              .getConversation(groupID: groupInfo.groupID, type: 2);
          ToastUtils.hideLoading();
          Get.toNamed(
            GetRouter.worldChat,
            arguments: conversation,
            parameters: {
              "groupNotice": worldGroupChatNotices[0],
            },
          );
        }
      } else {
        ToastUtils.showToast("当前聊天室已满，可以移步聊天室②畅聊");
      }
    } else {
      String groupID = worldGroupChatIds[1];
      bool success = await ChatIMManager.sharedInstance.joinGroup(
          groupType: GroupType.AVChatRoom, groupID: groupID, message: "");
      if (!success) {
        ToastUtils.hideLoading();
        ToastUtils.showToast("加入世界聊天群失败，请稍后重试");
      } else {
        V2TimConversation conversation = await ChatIMManager.sharedInstance
            .getConversation(groupID: groupID, type: 2);
        ToastUtils.hideLoading();
        Get.toNamed(
          GetRouter.worldChat,
          arguments: conversation,
          parameters: {
            "groupNotice": worldGroupChatNotices[1],
          },
        );
      }
    }
  }

  ///扩列发送打招呼消息
  String getRandomGreetMsg() {
    List<String> greetMsgs = [
      "扩列扩列咯，俺是扩列达人~",
      "那么多的人，刚好就是咱们碰到了一起，嘿嘿，是缘分吗？",
      "让我瞧瞧，怎么个事？",
      "每两个人的相遇，都是机缘注定的！不知道这句话是不是真的，所以我想试试~",
      "有木有一起扩列的小伙伴？",
      "行走江湖，朋友多最吃香，让我来扩个列~",
      "来个小伙伴一起玩！~",
    ];
    return greetMsgs[Random().nextInt(greetMsgs.length - 1)];
  }

  void toDetail(String? userTaskId, bool islook, TodoTogetherListEntity model) {
    Get.toNamed(GetRouter.todoTogetherDetail, arguments: {
      "todoId": userTaskId,
      "type": TodoTogetherListType.all,
      "isLook": islook,
      "model": model
    })?.then((v) {
      loadListData();
    });
  }

  void delete(String? id) async {
    bool? success =
        await ApiService().todoTogetherDelete(userTaskId: int.parse(id!));
    if (success) {
      data.remove(data.firstWhere((e) => e.userTaskId == id));
      if (Get.isDialogOpen == true) {
        Get.back();
      }
    } else {}
  }
}
