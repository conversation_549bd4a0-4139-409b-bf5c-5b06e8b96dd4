import 'package:dada/generated/assets.dart';
import 'package:dada/pages/splash/splash_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  final SplashController controller = Get.put(SplashController());

  @override
  void initState() {
    super.initState();

    ///检查是否已经登录
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if (mounted) {
    //     controller.checkIsLogined();
    //   }
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.white,
      body: ImageUtils.getImage(Assets.imagesSplash, ScreenUtil().screenWidth,
          ScreenUtil().screenHeight,
          fit: BoxFit.fill),
      // body: ImageUtils.getImage('img_start.webp', 1.sw, 1.sh, fit: BoxFit.cover),
    );
  }
}
