import 'dart:async';
import 'dart:io';

import 'package:dada/configs/app_config.dart';
import 'package:dada/global.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/ad/ad_plugin_manager.dart';
import 'package:dada/utils/login_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gromore_ads/flutter_gromore_ads.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';

class SplashController extends GetxController {
  late AdPluginListener _listener;

  @override
  void onInit() {
    super.onInit();
    addAdListener();
    _showSplashAd();
  }

  @override
  void onClose() {
    AdPluginManager.instance.removeListener(_listener);
    super.onClose();
  }

  void addAdListener() {
    _listener = AdPluginListener(
      onAdSkip: (adId) {
        checkIsLogined();
      },
      onAdClicked: (adId) {},
      onAdClosed: (adId) {
        checkIsLogined();
      },
      onAdComplete: (adId) {},
      onAdError: (adId) {
        FlutterNativeSplash.remove();
        checkIsLogined();
      },
      onAdExposure: (adId) {},
      onAdPresent: (adId) {
        FlutterNativeSplash.remove();
      },
      onAdLoaded: (adId) {},
    );
    AdPluginManager.instance.addListener(_listener);
  }

  void checkIsLogined() {
    FlutterNativeSplash.remove();
    if (LoginUtils.isLogined()) {
      Global.initServiceAfterLogin();
      // Get.offAllNamed(GetRouter.main);

      if (Platform.isAndroid) {
        Future.delayed(const Duration(milliseconds: 200), () {
          Get.offAllNamed(GetRouter.main);
        });
      } else {
        Get.offAllNamed(GetRouter.main);
      }
    } else {
      Get.offAllNamed(GetRouter.welcome);
    }
  }

  void _showSplashAd() async {
    await FlutterGromoreAds.showSplashAd(AppConfig.splashId, preload: false);
  }
}
