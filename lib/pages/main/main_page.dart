import 'package:dada/app.dart';
import 'package:dada/pages/chat/chat_page.dart';
import 'package:dada/pages/dynamic/dynamic_page.dart';
import 'package:dada/pages/global_speaker/global_screen_overlay.dart';
import 'package:dada/pages/home/<USER>';
import 'package:dada/pages/mine/mine_page.dart';
import 'package:dada/pages/small_room/small_room_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/tab_bar_item_entity.dart';
import 'package:dada/pages/main/custom_bottom_navigation_bar.dart';
import 'package:dada/pages/main/main_controller.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final MainController controller = Get.put(MainController());
  OverlayEntry? globalOverlayEntry;

  final List<Widget> children = [
    const HomePage(),
    // const DynamicSayPage(),
    const DynamicPage(),
    const SmallRoomPage(),
    const ChatPage(),
    const MinePage(),
  ];

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((mag) {
      showGlobalOverlay();

      Future.delayed(const Duration(milliseconds: 300), () {
        // 检查登录状态，避免在token过期时发起API调用
        if (controller.isUserLoggedIn()) {
          controller.getDadaTabUnreadCount();
          controller.getDynamicTabUnreadCount();
          controller.getChatTabUnreadCount();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      body: Column(
        children: [
          ///页面内容显示
          Expanded(
            child: Obx(
              () {
                return LazyLoadIndexedStack(
                  index: controller.currentIndex.value,
                  children: children,
                );
              },
            ),
          ),
        ],
      ),
      //底部导航TabBar
      bottomNavigationBar: Obx(
        () => CustomBottomNavigationBar(
          items: [
            ///home tab item
            TabBarItemEntity().copyWith(
              title: S.current.homeTabTitle,
              icon: Assets.imagesHomeTabIcon,
              selectedIcon: Assets.imagesHomeTabSelectedIcon,
              width: 26.w,
            ),

            ///circle tab item
            TabBarItemEntity().copyWith(
              title: "搭圈",
              icon: Assets.imagesCircleTabIcon,
              selectedIcon: Assets.imagesCircleTabSelectedIcon,
              width: 26.w,
              badgeCount: controller.dynamicTabBadgeCount.value,
            ),

            ///dada tab item
            TabBarItemEntity().copyWith(
              title: "小屋",
              icon: Assets.imagesDadaTabIcon,
              selectedIcon: Assets.imagesDadaTabSelectedIcon,
              width: 26.w,
              selectedWidth: 60.w,
              badgeCount: controller.dadaTabBadgeCount.value,
            ),

            ///chat tab item
            TabBarItemEntity().copyWith(
              title: S.current.chatTabTitle,
              icon: Assets.imagesChatTabIcon,
              selectedIcon: Assets.imagesChatTabSelectedIcon,
              width: 26.w,
              badgeCount: controller.chatTabBadgeCount.value,
            ),

            ///mine tab item
            TabBarItemEntity().copyWith(
              title: S.current.mineTabTitle,
              icon: Assets.imagesMineTabIcon,
              selectedIcon: Assets.imagesMineTabSelectedIcon,
              width: 26.w,
            ),
          ],
          currentIndex: controller.currentIndex.value,
          onTap: (index) {
            controller.switchTab(index);
          },
        ),
      ),
    );
  }

  void showGlobalOverlay() {
    OverlayEntry overlayEntry = OverlayEntry(builder: (context) {
      return const GlobalScreenOverlay();
    });
    MyAppState.navigatorKey.currentState?.overlay?.insert(overlayEntry);
    globalOverlayEntry = overlayEntry;
  }

  @override
  void dispose() {
    globalOverlayEntry?.remove();
    globalOverlayEntry = null;
    controller.dispose();
    super.dispose();
  }
}
