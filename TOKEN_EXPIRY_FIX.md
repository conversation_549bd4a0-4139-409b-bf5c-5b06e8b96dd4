# Token过期问题修复说明

## 问题描述

1. **账号在别处登录后，此处再打开App有时会卡在main页面**
   - token过期时调一次logout接口，也有正常返回登录页面的时候
   - 正常时token过期会调多次logout接口

2. **重新登录后IM未初始化**
   - 正常返回登录页面后能正常登录，但是IM未初始化，也登录不成功

## 问题根因分析

### 问题1：卡在main页面
1. **重复logout调用**：多个地方同时检测到token过期，导致重复调用`LoginUtils.logOut(force: true)`
2. **并发处理缺陷**：没有防止重复logout的机制
3. **IM初始化时机问题**：在token已过期的情况下仍尝试初始化IM

### 问题2：IM未初始化
1. **状态清理不完整**：logout时IM状态没有正确清理
2. **userSig过期**：重新登录后userSig可能仍然是旧的过期签名
3. **初始化时机错误**：IM初始化在用户信息设置完成前就开始

## 修复方案

### 1. 防止重复logout (LoginUtils.dart)
- 添加`_isLoggingOut`标志防止重复调用logout
- 确保logout过程的原子性
- 改进错误处理和状态清理

### 2. 改进IM管理器 (ChatIMManager.dart)
- 增强login方法的错误处理和状态检查
- 改进logout方法，确保状态正确清理
- 添加详细的调试日志
- 防止重复登录和初始化

### 3. 优化初始化时机 (Global.dart)
- 修改`initServiceAfterLogin`为异步方法
- 添加登录状态检查
- 延迟IM初始化，确保用户信息已设置完成

### 4. 完善用户数据清理 (UserService.dart)
- 改进`removeUser`方法，精确清理相关数据
- 避免使用`SpUtil.clear()`清理所有数据

### 5. 添加Token管理器 (TokenManager.dart)
- 新增TokenManager工具类
- 防止重复处理token过期
- 统一管理token相关状态

### 6. 优化登录流程 (LoginController.dart)
- 在登录成功后添加延迟，确保用户信息设置完成
- 改进注册和登录的初始化时机

## 修复后的流程

### Token过期处理流程
1. 检测到token过期 → TokenManager.handleTokenExpired()
2. 如果已在处理中，直接返回，避免重复处理
3. 执行logout流程，清理所有相关状态
4. 跳转到登录页面
5. 重置TokenManager状态

### 重新登录流程
1. 用户输入登录信息
2. 调用登录接口，获取新的token和userSig
3. 设置用户信息到UserService
4. 延迟100ms确保用户信息设置完成
5. 调用Global.initServiceAfterLogin()
6. 延迟300ms后初始化IM SDK
7. 跳转到主页面

### IM初始化流程
1. 检查用户信息是否存在
2. 检查IM SDK是否已初始化
3. 检查是否已在登录中（防重复）
4. 获取userSig并验证
5. 执行IM登录
6. 设置用户信息到IM
7. 初始化推送等服务

## 测试建议

1. **模拟token过期场景**
   - 在另一设备登录同一账号
   - 验证当前设备是否正确跳转到登录页面
   - 验证是否只调用一次logout接口

2. **测试重新登录**
   - 在token过期后重新登录
   - 验证IM是否正确初始化
   - 验证聊天功能是否正常

3. **并发测试**
   - 同时触发多个可能导致token过期的操作
   - 验证是否只执行一次logout流程

## 注意事项

1. 所有的print语句在生产环境中应该替换为适当的日志框架
2. 可以根据需要调整延迟时间（目前设置为100ms和300ms）
3. 建议添加更多的错误处理和重试机制
4. 可以考虑添加网络状态检查，避免在网络不稳定时重复处理
